/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/*********************************************************************
*                      M O D U L E     B O D Y                       *
**********************************************************************
* Title: USB Cable detection                                         *
*                                                                    *
* Filename: Usb_detect.c                                             *
*                                                                    *
* Target, platform: Common Platform, SW platform                     *
*                                                                    *
* Author: Anton Eidelman                                             *
*                                                                    *
* Description: This file handles CABLE DETECTION using GPIO          *
*                                                                    *
*                                                                    *
*********************************************************************/

#include "hal_cfg.h" /* affects the conditional compilation logic below */

/* _USB_CABLE_DETECTION_OPTIONAL_ :
 * Ensure a board with no USB detection capability (GPIO not connected) works:
 * - USB works
 * - USB clock is shutdown if no USB activity (enumeration) happens during the timeout period
 * NOTE:  this violates USB 1.1, sect.7.2.1 and 7.1.5 requirement that device may not provide power
 * (e.g. activate the pull-up resistor on D+ ) unless VBUS is connected. In order to meet this requirement
 * DISABLE the define below (cable detection GPIO MUST be connected).
 *
 * ! USB_CABLE_DETECTION_ENABLED :
 * ADDITIONALLY, ensure this module does not access any means of cable-detection, i.e. PMIC or GPIO
 */

#if !defined(USB_CABLE_DETECTION_ENABLED)
	#define _USB_CABLE_DETECTION_OPTIONAL_  /* Force this */
#else
    /* CQ00040548: keep optional (violates the standard requirements as explained above) until:
	 * - Tavor power-up issue with USB attached is solved (MiccoEB)
	 * - USBMgrTunnel.c/Stub implements the USBDeviceStatusGet() - see comments USBMgrTunnel.c for USBMGRDEVICEPLUGIN */
	#define _USB_CABLE_DETECTION_OPTIONAL_
#endif

/*
 * Use the below switch to force VBUS detection via PMIC EXTON in all configurations
 */
//#define USB_CABLE_DETECTION_VIA_PMIC  // defined in the mak file inc_plat.mak :Yarden - set to off  / Tavor - set to on

#define USB_NOACT_CHECK_COUNT 6 /*sometimes it takes too long for the host to recognize the device*/

#include "osa.h"
#include "utils.h"
#include "bsp.h"
#include "pmu.h"
#include "bsp_mux.h"

#include "usb_config.h"
#include "usb_device.h"

#include "PMChip.h"
#if defined (PERIPHERAL_CLOCKS_VIA_PRM_API)
	#include "prm.h"
#endif

// 添加USB PHY寄存器定义
#include "USB_PHY.h"
#include "charger_dpdm_config.h"  // 添加DPDM控制配置

extern void USBCableInit(GPIO_PinNumbers detGpio);

// USB PHY DPDM控制状态
static BOOL usbPhyDpdmHizEnabled = FALSE;
static OSATimerRef usbDpdmTimerRef = NULL;
static dpdm_state_e currentDpdmState = DPDM_STATE_UNKNOWN;

#if defined(_USB_CABLE_DETECTION_OPTIONAL_)
// The cable detection (GPIO) is not mandatory; if not present, USB will be shutdown on no-activity only
#define USB_ACTIVITY_TIMEOUT 1000
static OSATimerRef usbActTimerRef;
#endif

static USBCable_StatusE usbCablestatus = USB_CABLE_UNKNOWN;

static enum {
	USB_DET_NONE,
	USB_DET_GPIO,
	USB_DET_PMC
}bspUsbDetectMode;

/************************************************************************
* Function: bspUsbPhyDpdmSetHiZ
*************************************************************************
* Description: 设置USB PHY DPDM为高阻状态
* Parameters: enable - TRUE:高阻, FALSE:正常
* Return value: none
* Notes: 用于避免模组DPDM对充电IC检测的干扰
************************************************************************/
static void bspUsbPhyDpdmSetHiZ(BOOL enable)
{
    UINT32 regVal;

    if (enable && currentDpdmState != DPDM_STATE_HIZ) {
        // 设置DPDM为高阻状态
        currentDpdmState = DPDM_STATE_TRANSITION;

        // 通过USB PHY寄存器控制DPDM引脚状态
        regVal = BU_REG_READ(USB_PHY_DPDM_HIZ_REG);
        regVal &= ~USB_PHY_DPDM_HIZ_MASK;  // 先清除相关位
        regVal |= USB_PHY_DPDM_HIZ_VALUE;  // 设置高阻值
        BU_REG_WRITE(USB_PHY_DPDM_HIZ_REG, regVal);

        usbPhyDpdmHizEnabled = TRUE;
        currentDpdmState = DPDM_STATE_HIZ;

#if DPDM_DEBUG_LOG_ENABLE
        DPDM_LOG_INFO("USB PHY DPDM set to Hi-Z for charger IC detection");
#endif
    } else if (!enable && currentDpdmState != DPDM_STATE_NORMAL) {
        // 恢复DPDM正常状态
        currentDpdmState = DPDM_STATE_TRANSITION;

        regVal = BU_REG_READ(USB_PHY_DPDM_HIZ_REG);
        regVal &= ~USB_PHY_DPDM_HIZ_MASK;  // 清除高阻位
        regVal |= USB_PHY_DPDM_NORMAL_VALUE;  // 设置正常值
        BU_REG_WRITE(USB_PHY_DPDM_HIZ_REG, regVal);

        usbPhyDpdmHizEnabled = FALSE;
        currentDpdmState = DPDM_STATE_NORMAL;

#if DPDM_DEBUG_LOG_ENABLE
        DPDM_LOG_INFO("USB PHY DPDM restored to normal state");
#endif
    }
}

/************************************************************************
* Function: bspUsbDpdmTimerCallback
*************************************************************************
* Description: DPDM定时器回调函数
* Parameters: tmrId - 定时器ID
* Return value: none
* Notes: 用于在充电IC检测完成后恢复DPDM正常状态
************************************************************************/
static void bspUsbDpdmTimerCallback(UINT32 tmrId)
{
    // 恢复DPDM正常状态，让模组可以进行USB通信
    bspUsbPhyDpdmSetHiZ(FALSE);

    // 删除定时器
    if (usbDpdmTimerRef != NULL) {
        OSATimerDelete(usbDpdmTimerRef);
        usbDpdmTimerRef = NULL;
    }
}

static BOOL usbExtOnHigh = TRUE; // default TRUE so USB required resources will be allocated (when no cable detection is enabled, resources will not be free in any case).

#if defined (PERIPHERAL_CLOCKS_VIA_PRM_API)
static	PRM_ServiceE	_usb_Detected_Resource;
#endif

/************************************************************************
* Function: bspUsbCableNotification
*************************************************************************
* Description:
*
* Parameters:
*
* Return value: none
*
* Notes:
************************************************************************/
static void bspUsbCableNotification(USBCable_StatusE status)
{
	if(status != usbCablestatus)
	{
		//NB: USB device driver currently does not control it's own clock, so it's done explicitely here:
		if(status == USB_CABLE_IN)
		{
#if defined (PERIPHERAL_CLOCKS_VIA_PRM_API)
	     	PRMManage(_usb_Detected_Resource, PRM_RSRC_ALLOC);
#else
			PMUPeripheralBothClocks(BOTH_USB, PMU_ON);
#endif

			// USB插入时，先设置DPDM为高阻，让充电IC优先检测
			bspUsbPhyDpdmSetHiZ(TRUE);

			// 创建定时器，延时后恢复DPDM正常状态
			if (usbDpdmTimerRef == NULL) {
				OSATimerCreate(&usbDpdmTimerRef);
				OSATimerStart(usbDpdmTimerRef, DPDM_RESTORE_DELAY_MS, 0,
							  bspUsbDpdmTimerCallback, 0);
			}
		}

		USBDeviceCableDetectionNotify(status);

		if(status != USB_CABLE_IN)
		{
			// USB拔出时，清理定时器并恢复DPDM状态
			if (usbDpdmTimerRef != NULL) {
				OSATimerStop(usbDpdmTimerRef);
				OSATimerDelete(usbDpdmTimerRef);
				usbDpdmTimerRef = NULL;
			}

			// 确保DPDM恢复正常状态
			if (usbPhyDpdmHizEnabled) {
				bspUsbPhyDpdmSetHiZ(FALSE);
			}

#if defined (PERIPHERAL_CLOCKS_VIA_PRM_API)
	     	PRMManage(_usb_Detected_Resource, PRM_RSRC_FREE);
#else
			PMUPeripheralBothClocks(BOTH_USB, PMU_OFF);
#endif
		}

		usbCablestatus = status;
	}
}

#if defined(_USB_CABLE_DETECTION_OPTIONAL_)
static UINT8 usbInactiveCounter;
/************************************************************************
* Function: usbActivityCheck
*************************************************************************
* Description:  work around for USB detection (when detection is not
*				enabled via GPIO or PMC)
*
* Parameters:
*
* Return value: none
*
* Notes:
************************************************************************/
static void usbActivityCheck(UINT32 id)
{
	BOOL stopTimer = TRUE;
#if defined (INTEL_2CHIP_PLAT)
	if( (USBDeviceStatusGet() == USB_DEVICE_STATUS_NOT_CONNECTED) || (USBDeviceStatusGet() == USB_DEVICE_STATUS_RESET))
#else  //(INTEL_2CHIP_PLAT)
	if(USBDeviceStatusGet()==USB_DEVICE_STATUS_NOT_CONNECTED)
#endif //(INTEL_2CHIP_PLAT)
	{
		if((usbInactiveCounter++)>=USB_NOACT_CHECK_COUNT)
		{
			//USB Hardware Power down fix for lowering power consumption
			USBMgrDeviceUnplug();
			bspUsbCableNotification(USB_CABLE_OUT);
		}
		else
			stopTimer = FALSE; //check again in a while
	}
	if(stopTimer)
	{
		OSATimerStop(usbActTimerRef);
		OSATimerDelete(usbActTimerRef);
	}
}
#endif     /* _USB_CABLE_DETECTION_OPTIONAL_ */

/************************************************************************
* Function: bspPmcExtOnEventHandler
*************************************************************************
* Description: handler for the PMIC interrutp on USB Event
*
* Parameters:   eventRegs union of events and status registers
*
* Return value: none
*
* Notes: here we update the usbExtOnHigh - static in this module.
************************************************************************/
//#if defined (_TAVOR_HARBELL_) || defined (_TAVOR_BOERNE_) || defined(SILICON_PV2)  // MICCO A0 & MICCO B0
#if defined (_TAVOR_BOERNE_) || defined(SILICON_PV2)  // MICCO A0 & MICCO B0
static void bspPmcExtOnEventHandler(UINT64 eventRegs)
#else
static void bspPmcExtOnEventHandler(UINT32 eventRegs)
#endif
{
	PMCEvents ev;
   	ev.all = eventRegs;

#if !defined (_TAVOR_BOERNE_)
	if (ev.regs.eventA & EVENT_A_EXTON)
	{
		usbExtOnHigh = ev.regs.status & STATUS_EXTON_VAL ? TRUE : FALSE;
	}
#else //_TAVOR_BOERNE_
   	usbExtOnHigh = ev.regs.statusB & STATUS_USB_DEV_VAL ? TRUE : FALSE;
#endif  //_TAVOR_BOERNE_

	bspUsbCableNotification(usbExtOnHigh ? USB_CABLE_IN : USB_CABLE_OUT);
}

/************************************************************************
* Function: bspUsbDetectViaPmc
*************************************************************************
* Description: Read the PMIC status register to detect USB state
* 	(connected or not) and update usbExtOnHigh accordingly - static variable in this module.
*
* Parameters:  none
*
* Return value: none
*
* Notes: before calling this function you should a single call to bspUsbDetectPhase1 function,
* which will perform binding the relevant events.
************************************************************************/
static void bspUsbDetectViaPmc(void)
{
	UINT8 status;

#if !defined (_TAVOR_BOERNE_)
	PMCNotifyEventBind(EVENT_A_EXTON, bspPmcExtOnEventHandler);
	status = PMCReadRegisterBlocking(PMC_STATUS_A_REG_B);
 	usbExtOnHigh = status & STATUS_EXTON_VAL ? TRUE : FALSE;
#else
	PMCNotifyEventBind(EVENT_B_USB_DEV, bspPmcExtOnEventHandler);
	status = PMCReadRegisterBlocking(PMC_STATUS_B_REG_B);
	usbExtOnHigh = status & STATUS_USB_DEV_VAL ? TRUE : FALSE;
#endif

	bspUsbDetectMode = USB_DET_PMC;
}

/************************************************************************
* Function: bspUsbDetectViaGpio
*************************************************************************
* Description: Config the GPIO for USB detect.
*
* Parameters:  the GPIO pin for USB detect
*
* Return value: none
*
* Notes:
************************************************************************/
static void bspUsbDetectViaGpio(GPIO_PinNumbers gpio)
{
#if defined(_HERMON_B0_SILICON_) && !defined (INTEL_2CHIP_PLAT_BVD)
	ASSERT( gpio < EGPIO_PIN_0 ); // EGPIO does not support edge detection
	setMuxGpio( gpio - GPIO_PIN_0, 0 );
	updateGRs();
	USBCableInit( gpio );
	ASSERT( USBCableDetectionRegister(bspUsbCableNotification));
	bspUsbDetectMode = USB_DET_GPIO;
#endif
}
/************************************************************************
* Function: bspUsbDetectStatusGet
*************************************************************************
* Description: return the USB cable status
*
* Parameters:
*
* Return value: USBCable_StatusE
*
* Notes:
************************************************************************/
USBCable_StatusE bspUsbDetectStatusGet(void)
{
	switch(bspUsbDetectMode)
	{
		case USB_DET_GPIO :
			return USBCableStatusRead();
	    case USB_DET_PMC  :
			return ((usbExtOnHigh == TRUE) ? USB_CABLE_IN : USB_CABLE_OUT);
	}
	return USB_CABLE_OUT;
}

/***********************************************************************
* Function: bspUsbActivate
*************************************************************************
* Description: is being called from phase2 and activate the USB
 * 				via PMIC/GPIO/Auto via timer
*
* Parameters:   none
*
* Return value: none
* Notes: here we allocat/free the USB resource for power managment.
************************************************************************/
void bspUsbActivate(void)
{
#if defined(USB_CABLE_DETECTION_ENABLED)

	#if defined (PERIPHERAL_CLOCKS_VIA_PRM_API)
	_usb_Detected_Resource =  (USB_VER_1_1==USBDeviceGetUSBVersionInUse())? PRM_SRVC_UDC : PRM_SRVC_USB_20_CLIENT_OTG0_PV ;
	#endif

#if !defined(USB_CABLE_DETECTION_VIA_PMIC)
	BspBoardType		board_type;

	board_type = bspBoardType();

	if ( board_type == BOARD_MATHIS )
	{
   		bspUsbDetectViaPmc();
	}
	else if ( board_type == BOARD_HERMON_DVK )
	{
		#if !defined(MIPS_TEST) || defined(MIPS_TEST_RAM) //on PDK same GPIO is used for USB detection and for CS3 (MIPS)
			bspUsbDetectViaGpio(GPIO_PIN_49);
		#elif !defined(_USB_CABLE_DETECTION_OPTIONAL_)
			#error Cannot use USB cable detection with MIPS_TEST - pin allocation conflict
		#endif
	}
#else
   bspUsbDetectViaPmc();
#endif   /* USB_CABLE_DETECTION_VIA_PMIC */
#endif   /* USB_CABLE_DETECTION_ENABLED  */

#if !defined(_USB_CABLE_DETECTION_OPTIONAL_)
	bspUsbCableNotification (bspUsbDetectStatusGet());
#else
	bspUsbCableNotification(USB_CABLE_IN);
    OSATimerCreate(&usbActTimerRef);
    OSATimerStart(usbActTimerRef, USB_ACTIVITY_TIMEOUT, USB_ACTIVITY_TIMEOUT, usbActivityCheck, 0);
#endif
}
/***********************************************************************
* Function: bspUsbActive
*************************************************************************
*   retune the USB detection status - true - in / false - out.
************************************************************************/
BOOL bspUsbActive(void)
{
	return bspUsbDetectMode == USB_DET_NONE ? FALSE : TRUE;
}

/*
 * The below functions are useful for power-management debug on systems that do not support USB detection
 */

//ICAT EXPORTED FUNCTION - HW_PLAT,PM,UsbCableOut
void UsbCableOut(void)
{
	bspUsbCableNotification(USB_CABLE_OUT);
}

void UsbCableIn(void)
{
	bspUsbCableNotification(USB_CABLE_IN);
}

/************************************************************************
* Function: bspUsbDpdmSetHiZ
*************************************************************************
* Description: 外部接口 - 设置USB PHY DPDM为高阻状态
* Parameters: enable - TRUE:高阻, FALSE:正常
* Return value: none
* Notes: 供充电IC驱动调用，用于控制DPDM状态避免干扰
************************************************************************/
void bspUsbDpdmSetHiZ(BOOL enable)
{
    bspUsbPhyDpdmSetHiZ(enable);
}

/************************************************************************
* Function: bspUsbDpdmGetHiZStatus
*************************************************************************
* Description: 外部接口 - 获取USB PHY DPDM高阻状态
* Parameters: none
* Return value: TRUE:高阻状态, FALSE:正常状态
* Notes: 供充电IC驱动查询当前DPDM状态
************************************************************************/
BOOL bspUsbDpdmGetHiZStatus(void)
{
    return usbPhyDpdmHizEnabled;
}
