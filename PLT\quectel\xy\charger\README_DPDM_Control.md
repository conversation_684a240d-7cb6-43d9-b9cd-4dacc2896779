# USB DPDM控制功能说明

## 概述

本功能解决了模组DPDM信号线与充电IC DPDM信号线同时连接时的干扰问题。通过智能控制模组DPDM的高阻状态，确保充电IC能够正确检测USB设备类型并设置合适的IINDPM电流限制。

## 问题背景

- **硬件连接**：DPDM信号线同时连接到模组和充电IC
- **干扰问题**：模组的DPDM驱动会干扰充电IC的DPDM检测
- **影响结果**：充电IC无法正确识别USB设备类型，导致IINDPM设置不当

## 解决方案

### 1. 自动控制机制

当USB插入时：
1. 模组检测到USB连接
2. 立即将模组DPDM设置为高阻状态
3. 充电IC进行DPDM检测并设置IINDPM
4. 延时500ms后恢复模组DPDM正常状态
5. 模组开始正常USB通信

### 2. 手动控制接口

充电IC驱动可以主动控制DPDM状态：
```c
// 设置模组DPDM为高阻
hal_charger_dpdm_set_hiz(true);

// 恢复模组DPDM正常状态
hal_charger_dpdm_set_hiz(false);

// 查询当前DPDM状态
bool is_hiz = hal_charger_dpdm_get_hiz_status();
```

## 文件结构

```
PLT/quectel/xy/charger/
├── inc/
│   └── charger_dpdm_config.h      # DPDM控制配置文件
├── src/
│   └── sc8960x_charger.c          # 充电IC驱动(已修改)
├── test/
│   └── dpdm_control_test.c        # DPDM控制测试程序
└── README_DPDM_Control.md         # 本说明文档

PLT/csw/BSP/
├── inc/
│   └── bsp.h                      # BSP接口(已修改)
└── src/
    └── usb_detect.c               # USB检测模块(已修改)
```

## 配置参数

在 `charger_dpdm_config.h` 中可以调整以下参数：

```c
#define DPDM_RESTORE_DELAY_MS       500    // 恢复DPDM延时(ms)
#define DPDM_CHARGER_DETECT_TIME_MS 300    // 充电IC检测时间(ms)
#define DPDM_AUTO_CONTROL_ENABLE    1      // 自动控制开关
#define DPDM_MANUAL_CONTROL_ENABLE  1      // 手动控制开关
#define DPDM_DEBUG_LOG_ENABLE       1      // 调试日志开关
```

## API接口

### BSP层接口

```c
// 设置模组DPDM高阻状态
void bspUsbDpdmSetHiZ(BOOL enable);

// 获取模组DPDM高阻状态
BOOL bspUsbDpdmGetHiZStatus(void);
```

### 充电IC层接口

```c
// 设置模组DPDM高阻状态
void hal_charger_dpdm_set_hiz(bool enable);

// 获取模组DPDM高阻状态
bool hal_charger_dpdm_get_hiz_status(void);
```

## 工作时序

```
USB插入事件
    ↓
模组检测到USB连接
    ↓
立即设置模组DPDM为高阻 (< 10ms)
    ↓
充电IC进行DPDM检测 (约300ms)
    ↓
充电IC设置IINDPM电流限制
    ↓
延时500ms后恢复模组DPDM正常状态
    ↓
模组开始USB通信
```

## 测试验证

运行测试程序验证功能：

```c
// 在适当的地方调用测试函数
int result = dpdm_control_test_main();
```

测试包括：
- 基本高阻功能测试
- 手动控制接口测试
- 时序验证测试
- 压力测试(100次快速切换)

## 注意事项

1. **时序要求**：确保充电IC有足够时间完成DPDM检测
2. **兼容性**：不影响原有USB功能，只是延迟USB通信启动
3. **调试**：可通过日志开关控制调试信息输出
4. **寄存器配置**：根据实际硬件调整USB PHY寄存器地址和控制位

## 故障排除

### 问题1：充电IC仍然检测不到正确的USB类型
- 检查DPDM_RESTORE_DELAY_MS是否足够长
- 确认USB PHY寄存器配置正确
- 验证充电IC的DPDM检测时间

### 问题2：USB通信异常
- 检查DPDM是否正确恢复到正常状态
- 确认USB PHY寄存器恢复值正确
- 验证定时器是否正常工作

### 问题3：功能不生效
- 确认DPDM_AUTO_CONTROL_ENABLE已启用
- 检查USB检测事件是否正常触发
- 验证BSP接口是否正确调用

## 版本历史

- v1.0.0: 初始版本，实现基本DPDM控制功能
  - 自动控制机制
  - 手动控制接口
  - 配置参数支持
  - 测试程序
