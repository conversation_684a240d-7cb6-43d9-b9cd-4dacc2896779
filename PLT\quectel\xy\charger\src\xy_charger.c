#include "osa.h"
#include "ql_adc.h"
#include "sc8906x_charger.h"
#include "xy_charger.h"
#include "xy_charger_priv.h"

/* 静态分配栈 */
static OSSemaRef s_charger_sem = NULL;
static volatile xy_charge_status_t g_charger_status = XY_CHARGE_STATUS_UNKNOWN;

static uint8_t s_charger_stack[CHARGER_TASK_STACK_SIZE];
static OSATaskRef s_charger_task = NULL;

/* 充电状态和VBUS类型的字符串映射 */
static const char *chrg_status_str[] = {"IDLE", "PRECHG", "FASTCHG", "CHGDONE"};
static const char *vbus_type_str[] = {"No Input",
                                      "SDP (500mA)",
                                      "CDP (1.5A)",
                                      "DCP (2.4A)",
                                      "Reserved",
                                      "Unknown Adapter (500mA)",
                                      "Non-Standard Adapter",
                                      "OTG"};

xy_charge_status_t xy_charger_get_status(void)
{
    return g_charger_status;
}

int xy_charger_set_enable(uint8_t enable)
{
    int ret;
    ql_gpio_level_e level;

    /* 根据enable参数设置GPIO电平 - 拉低使能充电，拉高禁用充电 */
    level = enable ? GPIO_LOW : GPIO_HIGH;

    ret = ql_gpio_set_level(CHARGER_CE_PIN, level);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Set CHARGER_CE_PIN level failed: GPIO%d, level=%d, ret=%d",
                          CHARGER_CE_PIN, level, ret);
        return -1;
    }

    CHARGER_LOG_INFO("Charger %s: CE_PIN(GPIO%d) set to %s",
                     enable ? "ENABLED" : "DISABLED",
                     CHARGER_CE_PIN,
                     enable ? "LOW" : "HIGH");

    return 0;
}

/* GPIO初始化 */
static int xy_charger_gpio_init(void)
{
    int ret;

    /* 初始化充电使能引脚 */
    ret = ql_pin_set_func(CHARGER_CE_PIN_NUM, 0);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Set CE pin gpio func fail, PIN%d, ret=%d", CHARGER_CE_PIN_NUM, ret);
        return -1;
    }

    ret = ql_gpio_init(CHARGER_CE_PIN, GPIO_OUTPUT, PULL_NONE, GPIO_LOW);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("CHARGER_CE_PIN init failed: GPIO%d, ret=%d", CHARGER_CE_PIN, ret);
        return -1;
    }

    CHARGER_LOG_INFO("CHARGER_CE_PIN initialized: GPIO%d (PIN%d)", CHARGER_CE_PIN, CHARGER_CE_PIN_NUM);
    return 0;
}

static void charger_int_handler(void *ctx)
{
    OSASemaphoreRelease(s_charger_sem);
}

static int setup_charger_interrupt(void)
{
    int ret;

    /* 设置PIN为GPIO功能 */
    ret = ql_pin_set_func(CHARGER_INT_PIN_NUM, 0);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Set INT pin gpio func fail, PIN%d, ret=%d", CHARGER_INT_PIN_NUM, ret);
        return ret;
    }

    CHARGER_LOG_INFO("CHARGER_INT_PIN configured: GPIO%d (PIN%d)", CHARGER_INT_PIN, CHARGER_INT_PIN_NUM);

    /* 配置中断 */
    ql_gpio_int_cfg_t int_cfg = {
        .gpio_num = CHARGER_INT_PIN,
        .gpio_pull = PULL_NONE,
        .int_type = INT_EDGES_FALLING,
        .wakeup_en = 0,
        .db_en = INT_DEBOUNCE_DISABLE,
        .int_callback = charger_int_handler,
        .cb_ctx = NULL};

    ret = ql_int_register_ex(&int_cfg);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Register charger interrupt failed: %d", ret);
        return ret;
    }

    ret = ql_int_enable(CHARGER_INT_PIN);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Enable charger interrupt failed: %d", ret);
        ql_int_unregister(CHARGER_INT_PIN);
        return ret;
    }

    return 0;
}

static xy_charge_status_t parse_charge_status(uint8_t reg08)
{
    uint8_t chrg_stat;
    chrg_stat = (reg08 & REG08_CHRG_STAT_MASK) >> REG08_CHRG_STAT_SHIFT;

    switch (chrg_stat)
    {
    case REG08_CHRG_STAT_IDLE:
        return XY_CHARGE_STATUS_IDLE;

    case REG08_CHRG_STAT_PRECHG:
        return XY_CHARGE_STATUS_PRECHG;

    case REG08_CHRG_STAT_FASTCHG:
        return XY_CHARGE_STATUS_FASTCHG;

    case REG08_CHRG_STAT_CHGDONE:
        return XY_CHARGE_STATUS_CHGDONE;

    default:
        return XY_CHARGE_STATUS_UNKNOWN;
    }
}

static void print_status_regs(const uint8_t *status_regs)
{
    uint8_t reg08 = status_regs[STATUS_REG08_INDEX];
    uint8_t reg09 = status_regs[STATUS_REG09_INDEX];
    uint8_t reg0a = status_regs[STATUS_REG0A_INDEX];

    /* REG08 状态解析 */
    uint8_t vbus_stat = (reg08 & REG08_VBUS_STAT_MASK) >> REG08_VBUS_STAT_SHIFT;
    uint8_t chrg_stat = (reg08 & REG08_CHRG_STAT_MASK) >> REG08_CHRG_STAT_SHIFT;
    uint8_t pg_stat = (reg08 & REG08_PG_STAT_MASK) >> REG08_PG_STAT_SHIFT;
    uint8_t therm_stat = (reg08 & REG08_THERM_STAT_MASK) >> REG08_THERM_STAT_SHIFT;
    uint8_t vsys_stat = (reg08 & REG08_VSYS_STAT_MASK) >> REG08_VSYS_STAT_SHIFT;

    /* REG09 故障状态解析 */
    uint8_t wdt_fault = (reg09 & REG09_FAULT_WDT_MASK) >> REG09_FAULT_WDT_SHIFT;
    uint8_t boost_fault = (reg09 & REG09_FAULT_BOOST_MASK) >> REG09_FAULT_BOOST_SHIFT;
    uint8_t chrg_fault = (reg09 & REG09_FAULT_CHRG_MASK) >> REG09_FAULT_CHRG_SHIFT;
    uint8_t bat_fault = (reg09 & REG09_FAULT_BAT_MASK) >> REG09_FAULT_BAT_SHIFT;
    uint8_t ntc_fault = (reg09 & REG09_FAULT_NTC_MASK) >> REG09_FAULT_NTC_SHIFT;

    /* REG0A 状态解析 */
    uint8_t vbus_gd = (reg0a & REG0A_VBUS_GD_MASK) >> REG0A_VBUS_GD_SHIFT;
    uint8_t vindpm_stat = (reg0a & REG0A_VINDPM_STAT_MASK) >> REG0A_VINDPM_STAT_SHIFT;
    uint8_t iindpm_stat = (reg0a & REG0A_IINDPM_STAT_MASK) >> REG0A_IINDPM_STAT_SHIFT;
    uint8_t topoff_active = (reg0a & REG0A_TOPOFF_ACTIVE_MASK) >> REG0A_TOPOFF_ACTIVE_SHIFT;
    uint8_t acov_stat = (reg0a & REG0A_ACOV_STAT_MASK) >> REG0A_ACOV_STAT_SHIFT;

    CHARGER_LOG_INFO("=== Charger Status Registers ===");
    CHARGER_LOG_INFO("REG08=0x%02X: VBUS=%d, CHRG=%d, PG=%d, THERM=%d, VSYS=%d",
                     reg08, vbus_stat, chrg_stat, pg_stat, therm_stat, vsys_stat);
    CHARGER_LOG_INFO("REG09=0x%02X: WDT=%d, BOOST=%d, CHRG_FLT=%d, BAT=%d, NTC=%d",
                     reg09, wdt_fault, boost_fault, chrg_fault, bat_fault, ntc_fault);
    CHARGER_LOG_INFO("REG0A=0x%02X: VBUS_GD=%d, VINDPM=%d, IINDPM=%d, TOPOFF=%d, ACOV=%d",
                     reg0a, vbus_gd, vindpm_stat, iindpm_stat, topoff_active, acov_stat);

    /* 显示易读的充电状态和VBUS类型 */
    CHARGER_LOG_INFO("Charge Status: %s, VBUS Type: %s",
                     chrg_status_str[chrg_stat], vbus_type_str[vbus_stat]);
}

static void update_charger_status(void)
{
    int ret;
    uint8_t reg09_val2;
    xy_charge_status_t status;
    uint8_t status_regs[STATUS_REGS_COUNT];

    ret = hal_get_charger_regs(SC8960X_REG_08, status_regs, STATUS_REGS_COUNT);
    if (ret < 0)
    {
        CHARGER_LOG_ERROR("Failed to read status registers: %d", ret);
        return;
    }

    ret = hal_get_charger_regs(SC8960X_REG_09, &reg09_val2, 1);
    if (ret < 0)
    {
        CHARGER_LOG_ERROR("Failed to read REG09 second time: %d", ret);
        return;
    }

    status_regs[STATUS_REG09_INDEX] = reg09_val2;

    print_status_regs(status_regs);

    status = parse_charge_status(status_regs[STATUS_REG08_INDEX]);
    if (status != g_charger_status)
    {
        CHARGER_LOG_INFO("Charge status changed: %d -> %d", g_charger_status, status);
        g_charger_status = status;
    }
}

static void charger_task_entry(void *arg)
{
    CHARGER_LOG_INFO("Charger task start");

    while (1)
    {
        int rc = OSASemaphoreAcquire(s_charger_sem, OS_SUSPEND);
        if (rc == OS_SUCCESS)
        {
            update_charger_status();
        }
    }
}

/* 初始化 */
int xy_charger_init(void)
{
    int ret;

    if (s_charger_sem)
    {
        CHARGER_LOG_INFO("Already initialized");
        return 0;
    }

    CHARGER_LOG_INFO("XY Charger module init");

    ret = xy_charger_gpio_init();
    if (ret != 0)
    {
        CHARGER_LOG_ERROR("Charger GPIO init failed: ret=%d", ret);
        goto fail;
    }

    /* 初始化底层驱动 */
    ret = sc8960x_dev_init();
    if (ret != 0)
    {
        CHARGER_LOG_ERROR("sc8960x_dev_init failed: %d", ret);
        goto fail;
    }

    /* 读取初始充电状态 */
    update_charger_status();

    /* 创建信号量 */
    ret = OSASemaphoreCreate(&s_charger_sem, 0, OS_FIFO);
    if (ret != OS_SUCCESS)
    {
        CHARGER_LOG_ERROR("Create semaphore fail");
        goto fail;
    }

    /* 创建充电任务 */
    ret = OSATaskCreate(&s_charger_task, s_charger_stack, sizeof(s_charger_stack),
                        CHARGER_TASK_PRIORITY, CHARGER_TASK_NAME,
                        charger_task_entry, NULL);
    if (ret != OS_SUCCESS)
    {
        CHARGER_LOG_ERROR("Create charger task fail");
        goto fail;
    }

    /* 配置中断引脚 */
    ret = setup_charger_interrupt();
    if (ret != 0)
    {
        CHARGER_LOG_ERROR("setup_charger_interrupt failed: %d", ret);
        goto fail;
    }

    CHARGER_LOG_INFO("XY Charger initialized successfully");
    return 0;

fail:
    if (s_charger_task)
    {
        OSATaskDelete(s_charger_task);
        s_charger_task = NULL;
    }

    if (s_charger_sem)
    {
        OSASemaphoreDelete(s_charger_sem);
        s_charger_sem = NULL;
    }

    g_charger_status = XY_CHARGE_STATUS_UNKNOWN;
    return ret;
}
