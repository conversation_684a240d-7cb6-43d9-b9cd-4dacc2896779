/*
 * USB DPDM控制配置文件
 * 用于配置模组DPDM与充电IC DPDM的协调工作参数
 */

#ifndef __CHARGER_DPDM_CONFIG_H__
#define __CHARGER_DPDM_CONFIG_H__

// 添加必要的头文件
#include "USB_PHY.h"  // USB PHY寄存器定义

// 日志输出宏定义(需要在包含此文件前定义)
#ifndef pr_info
#define pr_info(format, ...)
#endif
#ifndef pr_debug
#define pr_debug(format, ...)
#endif
#ifndef pr_err
#define pr_err(format, ...)
#endif

// DPDM控制时序配置
#define DPDM_HIZ_DELAY_MS           100    // USB插入后，模组DPDM进入高阻的延时(ms)
#define DPDM_RESTORE_DELAY_MS       500    // 充电IC检测完成后，恢复模组DPDM的延时(ms)
#define DPDM_CHARGER_DETECT_TIME_MS 300    // 充电IC DPDM检测所需时间(ms)

// DPDM控制策略配置
#define DPDM_AUTO_CONTROL_ENABLE    1      // 是否启用自动DPDM控制 (1:启用, 0:禁用)
#define DPDM_MANUAL_CONTROL_ENABLE  1      // 是否启用手动DPDM控制接口 (1:启用, 0:禁用)

// USB PHY寄存器配置
#define USB_PHY_DPDM_HIZ_REG        USB2_PHY1_REG01    // DPDM高阻控制寄存器
#define USB_PHY_DPDM_HIZ_MASK       (0x3 << 6)         // DPDM高阻控制位掩码
#define USB_PHY_DPDM_HIZ_VALUE      (0x3 << 6)         // DPDM高阻设置值
#define USB_PHY_DPDM_NORMAL_VALUE   (0x0 << 6)         // DPDM正常设置值

// 调试配置
#define DPDM_DEBUG_LOG_ENABLE       1      // 是否启用DPDM控制调试日志 (1:启用, 0:禁用)

// 日志宏定义
#if DPDM_DEBUG_LOG_ENABLE
#define DPDM_LOG_INFO(format, ...)  pr_info("[DPDM]: " format, ##__VA_ARGS__)
#define DPDM_LOG_DBG(format, ...)   pr_debug("[DPDM]: " format, ##__VA_ARGS__)
#define DPDM_LOG_ERR(format, ...)   pr_err("[DPDM]: " format, ##__VA_ARGS__)
#else
#define DPDM_LOG_INFO(format, ...)  
#define DPDM_LOG_DBG(format, ...)   
#define DPDM_LOG_ERR(format, ...)   
#endif

// DPDM控制状态枚举
typedef enum {
    DPDM_STATE_UNKNOWN = 0,     // 未知状态
    DPDM_STATE_NORMAL,          // 正常状态，模组可以使用DPDM
    DPDM_STATE_HIZ,             // 高阻状态，充电IC可以使用DPDM
    DPDM_STATE_TRANSITION       // 过渡状态，正在切换中
} dpdm_state_e;

// DPDM控制事件枚举
typedef enum {
    DPDM_EVENT_USB_PLUGIN = 0,  // USB插入事件
    DPDM_EVENT_USB_PLUGOUT,     // USB拔出事件
    DPDM_EVENT_CHARGER_DETECT,  // 充电IC检测事件
    DPDM_EVENT_TIMER_EXPIRE,    // 定时器超时事件
    DPDM_EVENT_MANUAL_CONTROL   // 手动控制事件
} dpdm_event_e;

#endif /* __CHARGER_DPDM_CONFIG_H__ */
