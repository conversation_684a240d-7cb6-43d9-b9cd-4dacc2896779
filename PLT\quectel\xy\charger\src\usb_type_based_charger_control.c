/*
 * 基于模组USB设备类型检测的充电IC控制方案
 * 通过模组检测USB设备类型，直接控制充电IC的IINDPM设置
 */

#include "bsp.h"
#include "sc8906x_charger.h"
#include "charger_dpdm_config.h"
#include "UART.h"
#include "osa.h"

#define USB_TYPE_LOG(format, ...) CPUartLogPrintf("[USB_TYPE_CTRL]: " format, ##__VA_ARGS__)

// USB设备类型枚举（基于充电IC寄存器定义）
typedef enum {
    USB_TYPE_NONE = 0,          // 无输入
    USB_TYPE_SDP = 1,           // 标准下行端口 (500mA)
    USB_TYPE_CDP = 2,           // 充电下行端口 (1.5A)  
    USB_TYPE_DCP = 3,           // 专用充电端口 (2.4A)
    USB_TYPE_RESERVED = 4,      // 保留
    USB_TYPE_UNKNOWN = 5,       // 未知适配器 (500mA)
    USB_TYPE_NON_STANDARD = 6,  // 非标准适配器
    USB_TYPE_OTG = 7            // OTG
} usb_device_type_e;

// USB类型对应的IINDPM电流限制配置
typedef struct {
    usb_device_type_e type;
    int iindpm_current;         // mA
    const char *description;
} usb_type_config_t;

// USB类型配置表
static const usb_type_config_t usb_type_configs[] = {
    {USB_TYPE_NONE,         100,  "No Input"},
    {USB_TYPE_SDP,          500,  "SDP (500mA)"},
    {USB_TYPE_CDP,          1500, "CDP (1.5A)"},
    {USB_TYPE_DCP,          2400, "DCP (2.4A)"},
    {USB_TYPE_RESERVED,     500,  "Reserved"},
    {USB_TYPE_UNKNOWN,      500,  "Unknown Adapter (500mA)"},
    {USB_TYPE_NON_STANDARD, 1000, "Non-Standard Adapter"},
    {USB_TYPE_OTG,          500,  "OTG"}
};

// 全局变量
static usb_device_type_e current_usb_type = USB_TYPE_NONE;
static OSATimerRef usb_type_check_timer = NULL;
static BOOL usb_type_control_enabled = TRUE;

/************************************************************************
* Function: get_usb_type_config
*************************************************************************
* Description: 根据USB类型获取配置信息
* Parameters: type - USB设备类型
* Return value: 配置信息指针，NULL表示未找到
************************************************************************/
static const usb_type_config_t* get_usb_type_config(usb_device_type_e type)
{
    for (int i = 0; i < sizeof(usb_type_configs) / sizeof(usb_type_configs[0]); i++) {
        if (usb_type_configs[i].type == type) {
            return &usb_type_configs[i];
        }
    }
    return NULL;
}

/************************************************************************
* Function: read_usb_type_from_charger_ic
*************************************************************************
* Description: 从充电IC读取USB设备类型
* Parameters: none
* Return value: USB设备类型
* Notes: 读取充电IC的REG08寄存器获取VBUS类型
************************************************************************/
static usb_device_type_e read_usb_type_from_charger_ic(void)
{
    uint8_t reg08_val;
    int ret;
    
    ret = hal_get_charger_regs(SC8960X_REG_08, &reg08_val, 1);
    if (ret < 0) {
        USB_TYPE_LOG("Failed to read REG08: %d", ret);
        return USB_TYPE_NONE;
    }
    
    uint8_t vbus_stat = (reg08_val & REG08_VBUS_STAT_MASK) >> REG08_VBUS_STAT_SHIFT;
    return (usb_device_type_e)vbus_stat;
}

/************************************************************************
* Function: set_iindpm_based_on_usb_type
*************************************************************************
* Description: 根据USB类型设置IINDPM电流限制
* Parameters: type - USB设备类型
* Return value: 0:成功, -1:失败
************************************************************************/
static int set_iindpm_based_on_usb_type(usb_device_type_e type)
{
    const usb_type_config_t *config = get_usb_type_config(type);
    if (!config) {
        USB_TYPE_LOG("Unknown USB type: %d", type);
        return -1;
    }
    
    int ret = hal_set_charger_iindpm(config->iindpm_current);
    if (ret < 0) {
        USB_TYPE_LOG("Failed to set IINDPM to %dmA for %s", 
                    config->iindpm_current, config->description);
        return -1;
    }
    
    USB_TYPE_LOG("Set IINDPM to %dmA for USB type: %s", 
                config->iindpm_current, config->description);
    return 0;
}

/************************************************************************
* Function: usb_type_check_timer_callback
*************************************************************************
* Description: USB类型检查定时器回调函数
* Parameters: tmrId - 定时器ID
* Return value: none
************************************************************************/
static void usb_type_check_timer_callback(UINT32 tmrId)
{
    if (!usb_type_control_enabled) {
        return;
    }
    
    usb_device_type_e detected_type = read_usb_type_from_charger_ic();
    
    if (detected_type != current_usb_type) {
        USB_TYPE_LOG("USB type changed: %d -> %d", current_usb_type, detected_type);
        
        // 设置对应的IINDPM电流限制
        if (set_iindpm_based_on_usb_type(detected_type) == 0) {
            current_usb_type = detected_type;
        }
    }
}

/************************************************************************
* Function: start_usb_type_monitoring
*************************************************************************
* Description: 启动USB类型监控
* Parameters: none
* Return value: 0:成功, -1:失败
************************************************************************/
static int start_usb_type_monitoring(void)
{
    if (usb_type_check_timer != NULL) {
        USB_TYPE_LOG("USB type monitoring already started");
        return 0;
    }
    
    int ret = OSATimerCreate(&usb_type_check_timer);
    if (ret != OS_SUCCESS) {
        USB_TYPE_LOG("Failed to create USB type check timer");
        return -1;
    }
    
    // 每500ms检查一次USB类型
    ret = OSATimerStart(usb_type_check_timer, 500, 500, 
                       usb_type_check_timer_callback, 0);
    if (ret != OS_SUCCESS) {
        USB_TYPE_LOG("Failed to start USB type check timer");
        OSATimerDelete(usb_type_check_timer);
        usb_type_check_timer = NULL;
        return -1;
    }
    
    USB_TYPE_LOG("USB type monitoring started");
    return 0;
}

/************************************************************************
* Function: stop_usb_type_monitoring
*************************************************************************
* Description: 停止USB类型监控
* Parameters: none
* Return value: none
************************************************************************/
static void stop_usb_type_monitoring(void)
{
    if (usb_type_check_timer != NULL) {
        OSATimerStop(usb_type_check_timer);
        OSATimerDelete(usb_type_check_timer);
        usb_type_check_timer = NULL;
        USB_TYPE_LOG("USB type monitoring stopped");
    }
}

/************************************************************************
* Function: usb_type_based_charger_control_init
*************************************************************************
* Description: 初始化基于USB类型的充电控制
* Parameters: none
* Return value: 0:成功, -1:失败
************************************************************************/
int usb_type_based_charger_control_init(void)
{
    USB_TYPE_LOG("Initializing USB type based charger control");
    
    // 读取初始USB类型
    current_usb_type = read_usb_type_from_charger_ic();
    const usb_type_config_t *config = get_usb_type_config(current_usb_type);
    if (config) {
        USB_TYPE_LOG("Initial USB type: %s", config->description);
        set_iindpm_based_on_usb_type(current_usb_type);
    }
    
    // 启动监控
    return start_usb_type_monitoring();
}

/************************************************************************
* Function: usb_type_based_charger_control_deinit
*************************************************************************
* Description: 反初始化基于USB类型的充电控制
* Parameters: none
* Return value: none
************************************************************************/
void usb_type_based_charger_control_deinit(void)
{
    USB_TYPE_LOG("Deinitializing USB type based charger control");
    stop_usb_type_monitoring();
    current_usb_type = USB_TYPE_NONE;
}

/************************************************************************
* Function: usb_type_based_charger_control_enable
*************************************************************************
* Description: 启用/禁用基于USB类型的充电控制
* Parameters: enable - TRUE:启用, FALSE:禁用
* Return value: none
************************************************************************/
void usb_type_based_charger_control_enable(BOOL enable)
{
    usb_type_control_enabled = enable;
    USB_TYPE_LOG("USB type based control %s", enable ? "enabled" : "disabled");
    
    if (enable) {
        // 立即检查一次USB类型
        usb_type_check_timer_callback(0);
    }
}

/************************************************************************
* Function: get_current_usb_type
*************************************************************************
* Description: 获取当前检测到的USB类型
* Parameters: none
* Return value: 当前USB设备类型
************************************************************************/
usb_device_type_e get_current_usb_type(void)
{
    return current_usb_type;
}

/************************************************************************
* Function: get_current_usb_type_description
*************************************************************************
* Description: 获取当前USB类型的描述字符串
* Parameters: none
* Return value: 描述字符串，NULL表示未知类型
************************************************************************/
const char* get_current_usb_type_description(void)
{
    const usb_type_config_t *config = get_usb_type_config(current_usb_type);
    return config ? config->description : "Unknown";
}

/************************************************************************
* Function: force_usb_type_check
*************************************************************************
* Description: 强制执行一次USB类型检查
* Parameters: none
* Return value: 0:成功, -1:失败
************************************************************************/
int force_usb_type_check(void)
{
    if (!usb_type_control_enabled) {
        USB_TYPE_LOG("USB type control is disabled");
        return -1;
    }
    
    USB_TYPE_LOG("Force USB type check");
    usb_type_check_timer_callback(0);
    return 0;
}

/************************************************************************
* Function: print_usb_type_status
*************************************************************************
* Description: 打印当前USB类型状态信息
* Parameters: none
* Return value: none
************************************************************************/
void print_usb_type_status(void)
{
    const usb_type_config_t *config = get_usb_type_config(current_usb_type);
    
    USB_TYPE_LOG("=== USB Type Status ===");
    USB_TYPE_LOG("Control enabled: %s", usb_type_control_enabled ? "Yes" : "No");
    USB_TYPE_LOG("Current USB type: %d (%s)", current_usb_type, 
                config ? config->description : "Unknown");
    if (config) {
        USB_TYPE_LOG("IINDPM setting: %dmA", config->iindpm_current);
    }
    USB_TYPE_LOG("Monitoring: %s", usb_type_check_timer ? "Active" : "Inactive");
}
