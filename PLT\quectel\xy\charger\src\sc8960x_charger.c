#include "ql_iic.h"
#include "UART.h"
#include "sc8906x_charger.h"
#include "bsp.h"  // 添加BSP接口，用于DPDM控制

#define SC8960X_DRIVER_VERSION "1.0.0_SC"

#define SC89601D_PN 0x03

#define SC8960X_IIC_ADDR 0x6B
#define SC8960X_IIC_NUM 0 // 0:GPIO_49 SCL,GPIO_50 SDA

#define pr_info(format, ...) CPUartLogPrintf("[SC8960X/INFO]: " format, ##__VA_ARGS__)
#define pr_debug(format, ...) CPUartLogPrintf("[SC8960X/DBG]: " format, ##__VA_ARGS__)
#define pr_err(format, ...) CPUartLogPrintf("[SC8960X/ERR]: " format, ##__VA_ARGS__)

struct sc8960x_desc
{
    int vindpm;
    int iindpm;
    int ichg;
    int vbat;
    int iprechg;
    int iterm;

    enum vboost boostv; // options are 4850,
    enum iboost boosti; // options are 500mA, 1200mA
    enum vac_ovp vacovp;
    int ict;
};

static struct sc8960x_desc default_desc = {
    .iindpm = 2300,
    .ichg = 1980,
    .iterm = 120,
};

struct sc8960x_chip
{
    unsigned char i2c_channel;
    struct sc8960x_desc *desc;

    int pn_num;
};

static struct sc8960x_chip __chip;
static struct sc8960x_chip *chip = &__chip;

//---------------i2c api---------------
static int sc8960x_write_block(struct sc8960x_chip *chip,
                               uint8_t addr, uint8_t *data, uint8_t len)
{
    int ret;

    // 使用Quectel I2C接口写入数据
    ret = ql_i2c_write(chip->i2c_channel, SC8960X_IIC_ADDR, addr, data, len);
    if (ret)
    {
        pr_err("sc8960x_write_block : write reg %02x fail\n", addr);
    }

    return ret;
}

static int sc8960x_read_block(struct sc8960x_chip *chip,
                              uint8_t addr, uint8_t *data, uint8_t len)
{
    int ret;

    // 使用Quectel I2C接口读取数据
    ret = ql_i2c_read(chip->i2c_channel, SC8960X_IIC_ADDR, addr, data, len);
    if (ret)
    {
        pr_err("sc8960x_read_block : read reg %02x fail\n", addr);
    }

    return ret;
}

static int sc8960x_reg_write(struct sc8960x_chip *chip,
                             uint8_t addr, uint8_t data)
{
    return sc8960x_write_block(chip, addr, &data, 1);
}

static int sc8960x_reg_read(struct sc8960x_chip *chip,
                            uint8_t addr, uint8_t *data)
{
    return sc8960x_read_block(chip, addr, data, 1);
}

static int sc8960x_reg_update_bits(struct sc8960x_chip *chip,
                                   uint8_t addr, uint8_t mask, uint8_t val)
{
    int ret;
    uint8_t reg_val;

    ret = sc8960x_reg_read(chip, addr, &reg_val);
    if (ret)
    {
        return -1;
    }

    reg_val &= ~mask;
    reg_val |= val;

    return sc8960x_reg_write(chip, addr, reg_val);
}

//---------------chip api---------------
static int sc8960x_detect_device(struct sc8960x_chip *chip)
{
    int ret;
    uint8_t data;

    ret = sc8960x_reg_read(chip, SC8960X_REG_0B, &data);
    if (!ret)
    {
        chip->pn_num = (data & REG0B_PN_MASK) >> REG0B_PN_SHIFT;
        /*if (chip->pn_num != SC89601D_PN) {
            pr_err("%s: not find sc89601d, pn = %d\n", __func__, chip->pn_num);
            return -1;
        }*/
    }

    return ret;
}

static int sc8960x_enable_otg(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG01_OTG_ENABLE : REG01_OTG_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_01, REG01_OTG_CONFIG_MASK,
                                   val << REG01_OTG_CONFIG_SHIFT);
}

static int sc8960x_enable_charger(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG01_CHG_ENABLE : REG01_CHG_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_01, REG01_CHG_CONFIG_MASK,
                                   val << REG01_CHG_CONFIG_SHIFT);
}

static int sc8960x_set_charge_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t ichg;

    if (curr < REG02_ICHG_BASE)
    {
        curr = REG02_ICHG_BASE;
    }
    else if (curr > 3000)
    {
        curr = 3000;
    }

    pr_info("%s: %dmA\n", __func__, curr);

    ichg = (curr - REG02_ICHG_BASE) / REG02_ICHG_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_02, REG02_ICHG_MASK,
                                   ichg << REG02_ICHG_SHIFT);
}

static int sc8960x_set_term_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t iterm;

    if (curr < REG03_ITERM_BASE)
    {
        curr = REG03_ITERM_BASE;
    }
    else if (curr > 960)
    {
        curr = 960;
    }

    pr_info("%s: %dmA\n", __func__, curr);

    iterm = (curr - REG03_ITERM_BASE) / REG03_ITERM_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_03, REG03_ITERM_MASK,
                                   iterm << REG03_ITERM_SHIFT);
}

static int sc8960x_set_prechg_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t iprechg;

    if (curr < REG03_IPRECHG_BASE)
    {
        curr = REG03_IPRECHG_BASE;
    }
    else if (curr > 960)
    {
        curr = 960;
    }

    pr_info("%s: %dmA\n", __func__, curr);

    iprechg = (curr - REG03_IPRECHG_BASE) / REG03_IPRECHG_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_03, REG03_IPRECHG_MASK,
                                   iprechg << REG03_IPRECHG_SHIFT);
}

static int sc8960x_set_chargevolt(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    if (volt < REG04_VREG_BASE)
    {
        volt = REG04_VREG_BASE;
    }
    else if (volt > 4864)
    {
        volt = 4864;
    }

    pr_info("%s: %dmV\n", __func__, volt);

    val = (volt - REG04_VREG_BASE) / REG04_VREG_LSB;
    if ((volt - REG04_VREG_BASE) % REG04_VREG_LSB > 10)
        val += 1;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_04, REG04_VREG_MASK,
                                   val << REG04_VREG_SHIFT);
}

static int sc8960x_set_vindpm(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    pr_info("%s: %dmV\n", __func__, volt);

    if (volt < REG06_VINDPM_BASE)
    {
        val = 0;
    }
    else if (volt <= 5100)
    {
        val = (volt - REG06_VINDPM_BASE) / REG06_VINDPM_LSB;
    }
    else if (volt <= 8000)
    {
        val = 13;
    }
    else if (volt <= 8200)
    {
        val = 14;
    }
    else
    {
        val = 15;
    }

    return sc8960x_reg_update_bits(chip, SC8960X_REG_06, REG06_VINDPM_MASK,
                                   val << REG06_VINDPM_SHIFT);
}

static int sc8960x_set_iindpm(struct sc8960x_chip *chip, int curr)
{
    uint8_t val;

    if (curr < REG00_IINLIM_BASE)
    {
        curr = REG00_IINLIM_BASE;
    }
    else if (curr > 3200)
    {
        curr = 3200;
    }

    val = (curr - REG00_IINLIM_BASE) / REG00_IINLIM_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_00, REG00_IINLIM_MASK,
                                   val << REG00_IINLIM_SHIFT);
}

static int sc8960x_set_wdt(struct sc8960x_chip *chip, enum watchdog_t wdt)
{
    return sc8960x_reg_update_bits(chip, SC8960X_REG_05, REG05_WDT_MASK,
                                   wdt << REG05_WDT_SHIFT);
}

static int sc8960x_enter_hiz_mode(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG00_HIZ_ENABLE : REG00_HIZ_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_00, REG00_ENHIZ_MASK,
                                   val << REG00_ENHIZ_SHIFT);
}

static int sc8960x_enable_term(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG05_TERM_ENABLE : REG05_TERM_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_05, REG05_EN_TERM_MASK,
                                   val << REG05_EN_TERM_SHIFT);
}

static int sc8960x_set_boost_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t val = REG02_BOOST_LIM_0P5A;

    if (curr == BOOSTI_1200)
    {
        val = REG02_BOOST_LIM_1P2A;
    }

    return sc8960x_reg_update_bits(chip, SC8960X_REG_02, REG02_BOOST_LIM_MASK,
                                   val << REG02_BOOST_LIM_SHIFT);
}

static int sc8960x_set_boost_voltage(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    if (volt == BOOSTV_4850)
        val = REG06_BOOSTV_4P85V;
    else if (volt == BOOSTV_5150)
        val = REG06_BOOSTV_5P15V;
    else if (volt == BOOSTV_5300)
        val = REG06_BOOSTV_5P3V;
    else
        val = REG06_BOOSTV_5V;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_06, REG06_BOOSTV_MASK,
                                   val << REG06_BOOSTV_SHIFT);
}

static int sc8960x_set_acovp_threshold(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    if (volt == VAC_OVP_14000)
        val = REG06_OVP_14P0V;
    else if (volt == VAC_OVP_10500)
        val = REG06_OVP_10P5V;
    else if (volt == VAC_OVP_6500)
        val = REG06_OVP_6P5V;
    else
        val = REG06_OVP_5P5V;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_06, REG06_OVP_MASK,
                                   val << REG06_OVP_SHIFT);
}

static int sc8960x_set_vindpm_track(struct sc8960x_chip *chip, int track_volt)
{
    uint8_t val = track_volt;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_07, REG07_VDPM_BAT_TRACK_MASK,
                                   val << REG07_VDPM_BAT_TRACK_SHIFT);
}

static int sc8960x_reset_chip(struct sc8960x_chip *chip)
{
    uint8_t val = REG0B_REG_RESET << REG0B_REG_RESET_SHIFT;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_0B, REG0B_REG_RESET_MASK, val);
}

static int sc8960x_set_jeita_cool_temp(struct sc8960x_chip *chip, int temp)
{
    uint8_t val = temp;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_0C, REG0C_JEITA_COOL_TEMP_MASK,
                                   val << REG0C_JEITA_COOL_TEMP_SHIFT);
}

static int sc8960x_set_jeita_warm_iset(struct sc8960x_chip *chip, int percent)
{
    uint8_t val = percent;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_0C, REG0C_JEITA_WARM_ISET_MASK,
                                   val << REG0C_JEITA_WARM_ISET_SHIFT);
}

// 设置STAT引脚功能
static int sc8960x_set_stat_pin(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG00_STAT_CTRL_STAT : REG00_STAT_CTRL_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_00, REG00_STAT_CTRL_MASK, 
                                   val << REG00_STAT_CTRL_SHIFT);
}

// 设置BATFET_RST_EN功能
static int sc8960x_set_batfet_rst(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG07_BATFET_RST_ENABLE : REG07_BATFET_RST_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_07, REG07_BATFET_RST_EN_MASK, 
                                   val << REG07_BATFET_RST_EN_SHIFT);
}

//---------------system api---------------
int hal_set_charger_hiz(bool enable)
{
    return sc8960x_enter_hiz_mode(chip, enable);
}

int hal_set_charger_enable(bool enable)
{
    return sc8960x_enable_charger(chip, enable);
}

int hal_set_charger_otg(bool enable)
{
    return sc8960x_enable_otg(chip, enable);
}

int hal_set_charger_current(int curr)
{
    return sc8960x_set_charge_current(chip, curr);
}

int hal_set_charger_iindpm(int curr)
{
    return sc8960x_set_iindpm(chip, curr);
}

int hal_set_charger_vindpm(int volt)
{
    return sc8960x_set_vindpm(chip, volt);
}

int hal_get_charger_regs(uint8_t reg_addr, uint8_t *data, uint8_t len)
{
    return sc8960x_read_block(chip, reg_addr, data, len);
}

void sc8960x_dump_regs(void)
{
    int addr;
    uint8_t val;
    int ret;

    for (addr = 0x0; addr <= 0x0C; addr++)
    {
        ret = sc8960x_reg_read(chip, addr, &val);
        if (ret == 0)
            pr_info("%s: Reg[%02x] = 0x%02x\r\n", __func__, addr, val);
    }
}

static int sc8960x_hw_init(struct sc8960x_chip *chip)
{
    int ret;

    // 在充电IC初始化前，确保模组DPDM处于高阻状态，避免干扰充电IC的DPDM检测
    pr_info("%s: Setting module DPDM to Hi-Z for charger IC detection\n", __func__);
    bspUsbDpdmSetHiZ(TRUE);

    ret = sc8960x_reset_chip(chip);
    if (ret < 0)
        pr_err("%s: Failed to reset registers(%d)\n", __func__, ret);

    ret = sc8960x_set_wdt(chip, REG05_WDT_DISABLE);
    if (ret < 0)
        pr_err("%s: Failed to disable wdt timer(%d)\n", __func__, ret);

    ret = sc8960x_set_stat_pin(chip, FALSE);
    if (ret < 0)
        pr_err("%s: Failed to disable STAT pin(%d)\n", __func__, ret);

    ret = sc8960x_set_iindpm(chip, chip->desc->iindpm);
    if (ret < 0)
        pr_err("%s: Failed to set iindpm(%d)\n", __func__, ret);

    ret = sc8960x_set_charge_current(chip, chip->desc->ichg);
    if (ret < 0)
        pr_err("%s: Failed to set charge current(%d)\n", __func__, ret);

    ret = sc8960x_set_term_current(chip, chip->desc->iterm);
    if (ret < 0)
        pr_err("%s: Failed to set termination current(%d)\n", __func__, ret);

    ret = sc8960x_set_vindpm_track(chip, REG07_VDPM_BAT_TRACK_300MV);
    if (ret < 0)
        pr_err("%s: Failed to set vindpm track(%d)\n", __func__, ret);

    ret = sc8960x_set_jeita_cool_temp(chip, REG0C_JEITA_COOL_TEMP_15C);
    if (ret < 0)
        pr_err("%s: Failed to set jeita cool temp(%d)\n", __func__, ret);

    ret = sc8960x_set_jeita_warm_iset(chip, REG0C_JEITA_WARM_ISET_NOCHG);
    if (ret < 0)
        pr_err("%s: Failed to set jeita warm iset(%d)\n", __func__, ret);

    sc8960x_dump_regs();

    // 充电IC初始化完成后，恢复模组DPDM正常状态，允许USB通信
    // 注意：这里不立即恢复，而是让USB检测模块的定时器来控制恢复时机
    pr_info("%s: Charger IC initialization completed, DPDM will be restored by USB detection module\n", __func__);

    return ret;
}

int sc8960x_dev_init(void)
{
    int ret;

    pr_info("%s: ver: %s\n", __func__, SC8960X_DRIVER_VERSION);

    // 设置使用的I2C通道
    chip->i2c_channel = SC8960X_IIC_NUM;

    // 初始化I2C通道
    ret = ql_i2c_init(chip->i2c_channel, STANDARD_MODE);
    if (ret)
    {
        pr_err("%s: Failed to init i2c(%d)\n", __func__, ret);
        return ret;
    }

    ret = sc8960x_detect_device(chip);
    if (ret)
    {
        pr_err("%s: detect device failed(%d)\n", __func__, ret);
        return ret;
    }

    chip->desc = &default_desc;

    ret = sc8960x_hw_init(chip);

    pr_info("%s: sc8960x init %s\n", __func__,
            (ret == 0) ? "successful" : "failed");

    return ret;
}

/************************************************************************
* Function: hal_charger_dpdm_set_hiz
*************************************************************************
* Description: 充电IC专用接口 - 设置模组DPDM高阻状态
* Parameters: enable - true:高阻, false:正常
* Return value: none
* Notes: 供充电IC驱动调用，用于在DPDM检测时避免模组干扰
************************************************************************/
void hal_charger_dpdm_set_hiz(bool enable)
{
    bspUsbDpdmSetHiZ(enable ? TRUE : FALSE);
    pr_info("%s: Module DPDM set to %s\n", __func__, enable ? "Hi-Z" : "Normal");
}

/************************************************************************
* Function: hal_charger_dpdm_get_hiz_status
*************************************************************************
* Description: 充电IC专用接口 - 获取模组DPDM高阻状态
* Parameters: none
* Return value: true:高阻状态, false:正常状态
* Notes: 供充电IC驱动查询当前模组DPDM状态
************************************************************************/
bool hal_charger_dpdm_get_hiz_status(void)
{
    return bspUsbDpdmGetHiZStatus() ? true : false;
}
