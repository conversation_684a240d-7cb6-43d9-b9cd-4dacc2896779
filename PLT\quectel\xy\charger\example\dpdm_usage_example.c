/*
 * USB DPDM控制功能使用示例
 * 展示如何在实际项目中集成和使用DPDM控制功能
 */

#include "bsp.h"
#include "sc8906x_charger.h"
#include "charger_dpdm_config.h"
#include "UART.h"

#define EXAMPLE_LOG(format, ...) CPUartLogPrintf("[DPDM_EXAMPLE]: " format, ##__VA_ARGS__)

/************************************************************************
* Function: example_usb_plugin_handler
*************************************************************************
* Description: USB插入事件处理示例
* Parameters: none
* Return value: none
* Notes: 展示USB插入时的DPDM控制流程
************************************************************************/
void example_usb_plugin_handler(void)
{
    EXAMPLE_LOG("USB cable plugged in detected\n");
    
    // 方式1：依赖自动控制机制
    // USB检测模块会自动处理DPDM控制，无需额外代码
    
    // 方式2：手动控制(如果需要更精确的控制)
    #if DPDM_MANUAL_CONTROL_ENABLE
    {
        EXAMPLE_LOG("Manual DPDM control enabled\n");
        
        // 立即设置模组DPDM为高阻
        hal_charger_dpdm_set_hiz(true);
        EXAMPLE_LOG("Module DPDM set to Hi-Z\n");
        
        // 等待充电IC完成检测
        OSATaskSleep(DPDM_CHARGER_DETECT_TIME_MS);
        
        // 检查充电IC是否完成检测
        // 这里可以添加充电IC状态查询代码
        
        // 恢复模组DPDM正常状态
        hal_charger_dpdm_set_hiz(false);
        EXAMPLE_LOG("Module DPDM restored to normal\n");
    }
    #endif
    
    EXAMPLE_LOG("USB plugin handling completed\n");
}

/************************************************************************
* Function: example_usb_plugout_handler
*************************************************************************
* Description: USB拔出事件处理示例
* Parameters: none
* Return value: none
* Notes: 展示USB拔出时的清理流程
************************************************************************/
void example_usb_plugout_handler(void)
{
    EXAMPLE_LOG("USB cable plugged out detected\n");
    
    // 确保DPDM恢复正常状态
    if (hal_charger_dpdm_get_hiz_status()) {
        hal_charger_dpdm_set_hiz(false);
        EXAMPLE_LOG("Force restore module DPDM to normal\n");
    }
    
    EXAMPLE_LOG("USB plugout handling completed\n");
}

/************************************************************************
* Function: example_charger_init_with_dpdm_control
*************************************************************************
* Description: 带DPDM控制的充电IC初始化示例
* Parameters: none
* Return value: 0:成功, -1:失败
* Notes: 展示如何在充电IC初始化时集成DPDM控制
************************************************************************/
int example_charger_init_with_dpdm_control(void)
{
    int ret;
    
    EXAMPLE_LOG("Starting charger initialization with DPDM control\n");
    
    // 在充电IC初始化前，确保模组DPDM处于高阻状态
    // 这样可以避免初始化过程中的干扰
    hal_charger_dpdm_set_hiz(true);
    EXAMPLE_LOG("Set module DPDM to Hi-Z before charger init\n");
    
    // 初始化充电IC
    ret = sc8960x_dev_init();
    if (ret != 0) {
        EXAMPLE_LOG("Charger IC initialization failed: %d\n", ret);
        
        // 初始化失败时恢复DPDM状态
        hal_charger_dpdm_set_hiz(false);
        return ret;
    }
    
    EXAMPLE_LOG("Charger IC initialization successful\n");
    
    // 延时一段时间，确保充电IC完成所有检测
    OSATaskSleep(DPDM_CHARGER_DETECT_TIME_MS + 100);
    
    // 恢复模组DPDM正常状态
    hal_charger_dpdm_set_hiz(false);
    EXAMPLE_LOG("Module DPDM restored to normal after charger init\n");
    
    return 0;
}

/************************************************************************
* Function: example_dpdm_status_monitor
*************************************************************************
* Description: DPDM状态监控示例
* Parameters: none
* Return value: none
* Notes: 展示如何监控DPDM状态变化
************************************************************************/
void example_dpdm_status_monitor(void)
{
    static bool last_hiz_status = false;
    bool current_hiz_status;
    
    // 获取当前DPDM状态
    current_hiz_status = hal_charger_dpdm_get_hiz_status();
    
    // 检查状态是否发生变化
    if (current_hiz_status != last_hiz_status) {
        EXAMPLE_LOG("DPDM status changed: %s -> %s\n",
                   last_hiz_status ? "Hi-Z" : "Normal",
                   current_hiz_status ? "Hi-Z" : "Normal");
        
        last_hiz_status = current_hiz_status;
        
        // 根据状态变化执行相应操作
        if (current_hiz_status) {
            // 进入高阻状态
            EXAMPLE_LOG("Module DPDM is now in Hi-Z state\n");
            // 可以在这里添加相关处理代码
        } else {
            // 恢复正常状态
            EXAMPLE_LOG("Module DPDM is now in normal state\n");
            // 可以在这里添加相关处理代码
        }
    }
}

/************************************************************************
* Function: example_dpdm_emergency_restore
*************************************************************************
* Description: DPDM紧急恢复示例
* Parameters: none
* Return value: none
* Notes: 展示在异常情况下如何强制恢复DPDM状态
************************************************************************/
void example_dpdm_emergency_restore(void)
{
    EXAMPLE_LOG("Emergency DPDM restore triggered\n");
    
    // 强制恢复DPDM正常状态
    hal_charger_dpdm_set_hiz(false);
    
    // 等待一段时间确保状态稳定
    OSATaskSleep(50);
    
    // 验证状态是否正确恢复
    if (!hal_charger_dpdm_get_hiz_status()) {
        EXAMPLE_LOG("Emergency DPDM restore successful\n");
    } else {
        EXAMPLE_LOG("Emergency DPDM restore failed - hardware issue?\n");
    }
}

/************************************************************************
* Function: example_dpdm_configuration_check
*************************************************************************
* Description: DPDM配置检查示例
* Parameters: none
* Return value: none
* Notes: 展示如何检查DPDM控制配置
************************************************************************/
void example_dpdm_configuration_check(void)
{
    EXAMPLE_LOG("=== DPDM Configuration Check ===\n");
    
    EXAMPLE_LOG("Auto control: %s\n", 
               DPDM_AUTO_CONTROL_ENABLE ? "Enabled" : "Disabled");
    EXAMPLE_LOG("Manual control: %s\n", 
               DPDM_MANUAL_CONTROL_ENABLE ? "Enabled" : "Disabled");
    EXAMPLE_LOG("Debug log: %s\n", 
               DPDM_DEBUG_LOG_ENABLE ? "Enabled" : "Disabled");
    
    EXAMPLE_LOG("Timing configuration:\n");
    EXAMPLE_LOG("  Restore delay: %d ms\n", DPDM_RESTORE_DELAY_MS);
    EXAMPLE_LOG("  Charger detect time: %d ms\n", DPDM_CHARGER_DETECT_TIME_MS);
    
    EXAMPLE_LOG("Current DPDM status: %s\n",
               hal_charger_dpdm_get_hiz_status() ? "Hi-Z" : "Normal");
    
    EXAMPLE_LOG("=== Configuration Check Complete ===\n");
}

/************************************************************************
* Function: example_main
*************************************************************************
* Description: 示例主函数
* Parameters: none
* Return value: none
* Notes: 展示完整的使用流程
************************************************************************/
void example_main(void)
{
    EXAMPLE_LOG("=== DPDM Control Usage Example Started ===\n");
    
    // 1. 检查配置
    example_dpdm_configuration_check();
    
    // 2. 初始化充电IC(带DPDM控制)
    if (example_charger_init_with_dpdm_control() != 0) {
        EXAMPLE_LOG("Charger initialization failed, aborting example\n");
        return;
    }
    
    // 3. 模拟USB插入事件
    EXAMPLE_LOG("\n--- Simulating USB plugin event ---\n");
    example_usb_plugin_handler();
    
    // 4. 监控DPDM状态
    EXAMPLE_LOG("\n--- Monitoring DPDM status ---\n");
    for (int i = 0; i < 5; i++) {
        example_dpdm_status_monitor();
        OSATaskSleep(100);
    }
    
    // 5. 模拟USB拔出事件
    EXAMPLE_LOG("\n--- Simulating USB plugout event ---\n");
    example_usb_plugout_handler();
    
    // 6. 测试紧急恢复
    EXAMPLE_LOG("\n--- Testing emergency restore ---\n");
    hal_charger_dpdm_set_hiz(true);  // 先设置为高阻
    OSATaskSleep(100);
    example_dpdm_emergency_restore();
    
    EXAMPLE_LOG("\n=== DPDM Control Usage Example Completed ===\n");
}

/*
 * 使用说明：
 * 
 * 1. 在系统初始化时调用 example_charger_init_with_dpdm_control()
 * 2. 在USB事件处理中调用相应的处理函数
 * 3. 可以定期调用 example_dpdm_status_monitor() 监控状态
 * 4. 在异常情况下可以调用 example_dpdm_emergency_restore()
 * 
 * 注意事项：
 * - 确保在调用这些函数前已经初始化了相关的硬件模块
 * - 根据实际硬件调整延时参数
 * - 在生产环境中可以移除调试日志输出
 */
